{"name": "apphero-sf-sync-service", "version": "1.0.0", "description": "<!-- title: 'AWS Simple HTTP Endpoint example in NodeJS' description: 'This template demonstrates how to make a simple HTTP API with Node.js running on AWS Lambda and API Gateway using the Serverless Framework.' layout: Doc framework: v3 platform: AWS language: nodeJS authorLink: 'https://github.com/serverless' authorName: 'Serverless, inc.' authorAvatar: 'https://avatars1.githubusercontent.com/u/13742415?s=200&v=4' -->", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-dynamodb": "^3.665.0", "@aws-sdk/client-secrets-manager": "^3.552.0", "@gus-eip/loggers": "^4.1.3", "aws-sdk": "^2.1692.0", "axios": "^1.6.8", "dotenv": "^16.4.5", "fs": "^0.0.1-security", "parquetjs": "^0.11.2", "path": "^0.12.7", "uuid": "^10.0.0"}}