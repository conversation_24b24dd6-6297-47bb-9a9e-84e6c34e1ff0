const salesforce = require("../common/salesforce.service");
const { v4: uuidv4 } = require("uuid");
const {
  DynamoDBClient,
  TransactWriteItemsCommand,
} = require("@aws-sdk/client-dynamodb");
const { LoggerService } = require("../common/logger.service");
const { ErrorAccumulator } = require("../common/error-accumulator");
const { ErrorReporter } = require("../common/error-reporter");
const {
  SYNC_EVENTS,
  SYNC_OPERATIONS,
  ENTITY_TYPES,
} = require("../constants/syncEvents");

module.exports.handler = async (event) => {
  const requestId = uuidv4();
  const logger = new LoggerService();
  const errorAccumulator = new ErrorAccumulator();
  const errorReporter = new ErrorReporter(
    requestId,
    SYNC_OPERATIONS.SYNC_CMS_CONTENT
  );

  // Log CMS sync initiation
  await logger.logSync(
    SYNC_EVENTS.CMS_SYNC_INITIATED,
    event,
    {},
    "CMS content sync process initiated",
    requestId,
    "",
    "",
    null,
    SYNC_OPERATIONS.SYNC_CMS_CONTENT
  );

  console.log(`CMS sync started with request ID: ${requestId}`);

  try {
    errorAccumulator.incrementProcessed();

    // Fetch CMS data with error handling
    let cmsData;
    try {
      cmsData = await getCMSData(process.env.APPHERO_CMS_SYNC_CHANNEL_ID);
      errorAccumulator.addSuccess(
        "CMS_Data_Fetch",
        "CMS",
        process.env.APPHERO_CMS_SYNC_CHANNEL_ID
      );

      await logger.logSync(
        SYNC_EVENTS.CMS_DATA_FETCH_SUCCESS,
        { channelId: process.env.APPHERO_CMS_SYNC_CHANNEL_ID },
        { itemCount: cmsData.items?.length || 0 },
        `CMS data fetched successfully. Items: ${cmsData.items?.length || 0}`,
        requestId,
        "cms_channel",
        "channelId",
        cmsData,
        SYNC_OPERATIONS.SYNC_CMS_CONTENT
      );
    } catch (error) {
      errorAccumulator.addError(
        "CMS_Data_Fetch",
        "CMS",
        process.env.APPHERO_CMS_SYNC_CHANNEL_ID,
        error
      );

      await logger.errorSync(
        SYNC_EVENTS.CMS_DATA_FETCH_FAILED,
        { channelId: process.env.APPHERO_CMS_SYNC_CHANNEL_ID },
        error,
        `Failed to fetch CMS data: ${error.message}`,
        requestId,
        "cms_channel",
        "channelId",
        error,
        SYNC_OPERATIONS.SYNC_CMS_CONTENT
      );

      throw error; // Re-throw to exit early if CMS data fetch fails
    }

    // Process CMS data with error handling
    let transformedCmsFeedData = [];
    let transformedCmsEventData = [];

    try {
      const cmsFeedData = cmsData.items.filter(
        (item) =>
          (item.contentNodes.Is_Active?.value == "1" ||
            item.contentNodes.Is_Active?.value == "yes") &&
          item.type == "App_Hero"
      );
      const cmsEventData = cmsData.items.filter((item) => item.type == "Event");

      transformedCmsFeedData = updateMappingCountryInstitution(cmsFeedData);
      transformedCmsEventData = constructCMSEventObject(cmsEventData);

      errorAccumulator.addSuccess(
        "CMS_Data_Transform",
        "CMS",
        `${cmsFeedData.length}_feeds_${cmsEventData.length}_events`
      );
      console.log(
        `Transformed ${transformedCmsFeedData.length} feed items and ${transformedCmsEventData.length} event items`
      );
    } catch (error) {
      errorAccumulator.addError(
        "CMS_Data_Transform",
        "CMS",
        "data_transformation",
        error
      );
      console.error("Error transforming CMS data:", error);
      throw error;
    }

    // Bulk upsert with error handling
    try {
      await bulkUpsert(process.env.APPHERO_CMS_SYNC_TABLE_NAME, [
        ...transformedCmsFeedData,
        ...transformedCmsEventData,
      ]);

      errorAccumulator.addSuccess(
        "CMS_Bulk_Upsert",
        "CMS",
        `${
          transformedCmsFeedData.length + transformedCmsEventData.length
        }_items`
      );

      await logger.logSync(
        SYNC_EVENTS.CMS_BULK_UPSERT_SUCCESS,
        {
          feedItems: transformedCmsFeedData.length,
          eventItems: transformedCmsEventData.length,
          totalItems:
            transformedCmsFeedData.length + transformedCmsEventData.length,
        },
        { tableName: process.env.APPHERO_CMS_SYNC_TABLE_NAME },
        `CMS bulk upsert completed successfully. Total items: ${
          transformedCmsFeedData.length + transformedCmsEventData.length
        }`,
        requestId,
        "cms_table",
        "tableName",
        { success: true },
        SYNC_OPERATIONS.SYNC_CMS_CONTENT
      );

      console.log("Bulk upsert completed successfully.");
    } catch (error) {
      errorAccumulator.addError(
        "CMS_Bulk_Upsert",
        "CMS",
        process.env.APPHERO_CMS_SYNC_TABLE_NAME,
        error
      );

      await logger.errorSync(
        SYNC_EVENTS.CMS_BULK_UPSERT_FAILED,
        {
          feedItems: transformedCmsFeedData.length,
          eventItems: transformedCmsEventData.length,
          totalItems:
            transformedCmsFeedData.length + transformedCmsEventData.length,
        },
        error,
        `CMS bulk upsert failed: ${error.message}`,
        requestId,
        "cms_table",
        "tableName",
        error,
        SYNC_OPERATIONS.SYNC_CMS_CONTENT
      );

      throw error;
    }
  } catch (error) {
    errorAccumulator.addError("CMS_Sync", "CMS", "general", error);
    console.error("Error in CMS sync:", error);
  } finally {
    // Generate comprehensive error report using ErrorReporter
    const comprehensiveReport = await errorReporter.generateComprehensiveReport(
      errorAccumulator,
      event,
      "CMS Content Sync"
    );

    // Log error summary by entity type for detailed analysis
    await errorReporter.logErrorSummaryByEntity(errorAccumulator, event);

    // Generate notification content for monitoring systems
    const notificationContent = errorReporter.generateNotificationContent(
      errorAccumulator,
      "CMS Content Sync"
    );

    console.log(`CMS sync completed. Notification:`, notificationContent);

    // Return appropriate status based on error presence
    return comprehensiveReport.hasErrors
      ? "CMS sync completed with errors"
      : "CMS sync completed successfully";
  }
};

const getCMSData = async (channelId, isTotalRequired = true) => {
  try {
    const response = await salesforce.executeAPI(
      `connect/cms/delivery/channels/${channelId}/contents/query`,
      "GET",
      null,
      isTotalRequired,
      false
    );
    return response;
  } catch (error) {
    console.error("Error retrieving data from SF CMS:", error);
    throw error;
  }
};

const updateMappingCountryInstitution = (data) => {
  try {
    return data.flatMap((item) => {
      const countries = item.contentNodes.Applicable_Countries?.value
        .replace(/\s*,\s*/g, ",")
        .split(",");
      const institutions = item.contentNodes.Applicable_Institutions?.value
        .replace(/\s*,\s*/g, ",")
        .split(",");
      const statuses = item.contentNodes.Applicable_Statuses?.value
        .replace(/\s*,\s*/g, ",")
        .split(",");

      return countries.flatMap((country) =>
        institutions.flatMap((institution) =>
          statuses.map((status) => {
            const recId = uuidv4();
            return {
              PK: `CMSFeed#${country}#${institution}#${status}`,
              SK: `${item.contentKey}`,
              Title: item.title,
              Description: item.contentNodes.Description?.value,
              Image_Link__c: item.contentNodes.Image_Link?.value,
              Hyper_Link__c: item.contentNodes.Hyper_Link?.value,
              Published_Date__c: item.publishedDate,
              Is_Active__c: item.contentNodes.Is_Active?.value,
              Applicable_Country__c: country,
              Applicable_Institution__c: institution,
              Applicable_Status__c: status ? parseInt(status, 10) : "",
              Pages__c: item.contentNodes.Pages?.value,
              ContentKey: item.contentKey,
            };
          })
        )
      );
    });
  } catch (error) {
    console.log(
      "Error occurred at data mapping(updateMapCountryInstitution): ",
      error
    );
    throw error;
  }
};

const constructCMSEventObject = (data) => {
  try {
    return data.flatMap((item) => {
      const countries = item.contentNodes.Country?.value
        .replace(/\s*,\s*/g, ",")
        .split(",");
      const institutions = item.contentNodes.Institution?.value
        .replace(/\s*,\s*/g, ",")
        .split(",");

      return countries.flatMap((country) =>
        institutions.flatMap((institution) => {
          return {
            PK: `CMSEvent#${country}#${institution}`,
            SK: `${item.contentKey}`,
            Title: item.contentNodes.Event_Title?.value,
            Description: item.contentNodes.Event_Description?.value,
            Registration_Link__c: item.contentNodes.Registration_Link?.value,
            Thumbnail_Image__c: item.contentNodes.Thumbnail_Image?.value,
            Event_Date_Time__c:
              item.contentNodes.Event_Date_Time?.dateTimeValue,
            Is_Active__c: "yes",
            Applicable_Country__c: country,
            Applicable_Institution__c: institution,
            ContentKey: item.contentKey,
          };
        })
      );
    });
  } catch (error) {
    console.log(
      "Error occurred at data mapping(constructCMSEventObject): ",
      error
    );
    throw error;
  }
};

const bulkUpsert = async (tableName, items) => {
  const transactions = [];
  try {
    const client = new DynamoDBClient({ region: process.env.REGION });
    const tranactionDate = new Date().toISOString();
    for (const item of items) {
      const updateCommand = {
        Update: {
          TableName: tableName,
          Key: {
            PK: { S: item.PK },
            SK: { S: item.SK },
          },
          UpdateExpression:
            "SET Title = :title, Description = :description, Image_Link__c = :imageLink, Hyper_Link__c = :hyperLink, Published_Date__c = :publishedDate, Is_Active__c = :isActive, Applicable_Country__c = :applicableCountry, Applicable_Institution__c = :applicableInstitution, Applicable_Status__c = :applicableStatus, Pages__c = :pages, ContentKey = :contentKey, Updated_Date = :updatedDate, Registration_Link__c = :registrationLink, Thumbnail_Image__c = :thumbnailImage, Event_Date_Time__c = :eventDateTime",
          ExpressionAttributeValues: {
            ":title": { S: item.Title || "" },
            ":description": { S: item.Description || "" },
            ":imageLink": { S: item.Image_Link__c || "" },
            ":hyperLink": { S: item.Hyper_Link__c || "" },
            ":publishedDate": { S: item.Published_Date__c || "" },
            ":isActive": { S: item.Is_Active__c },
            ":applicableCountry": { S: item.Applicable_Country__c || "" },
            ":applicableInstitution": {
              S: item.Applicable_Institution__c || "",
            },
            ":applicableStatus": {
              S:
                item.Applicable_Status__c !== undefined
                  ? item.Applicable_Status__c.toString()
                  : "",
            },
            ":pages": { S: item.Pages__c || "" },
            ":contentKey": { S: item.ContentKey || "" },
            ":updatedDate": { S: tranactionDate || "" },
            ":registrationLink": {
              S: item.Registration_Link__c || "",
            },
            ":thumbnailImage": {
              S: item.Thumbnail_Image__c || "",
            },
            ":eventDateTime": {
              S: item.Event_Date_Time__c || "",
            },
          },
        },
      };
      transactions.push(updateCommand);
    }

    const chunkedTransactions = [];
    for (let i = 0; i < transactions.length; i += 25) {
      chunkedTransactions.push(transactions.slice(i, i + 25));
    }

    for (const chunk of chunkedTransactions) {
      const command = new TransactWriteItemsCommand({ TransactItems: chunk });
      await client.send(command);
    }
  } catch (error) {
    console.log("Error occued at dynamodb write:", error);
    throw error;
  }
};
