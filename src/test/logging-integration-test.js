/**
 * Simple test to demonstrate the logging integration
 * This file shows how to use the logging services in the sync handlers
 */

const { LoggerService } = require("../common/logger.service");
const { ErrorAccumulator } = require("../common/error-accumulator");
const { ErrorReporter } = require("../common/error-reporter");
const { SYNC_EVENTS, SYNC_OPERATIONS } = require("../constants/syncEvents");
const { v4: uuidv4 } = require("uuid");

// Mock environment variables for testing
process.env.REGION = process.env.REGION || "eu-west-1";
process.env.LOGGER_LOG_GROUP_NAME = process.env.LOGGER_LOG_GROUP_NAME || "apphero-sf-sync-service-test";
process.env.TEAMS_WEBHOOK_URL = process.env.TEAMS_WEBHOOK_URL || "";

async function testLoggingIntegration() {
  console.log("🧪 Testing Logging Integration...");
  
  const requestId = uuidv4();
  const logger = new LoggerService();
  const errorAccumulator = new ErrorAccumulator();
  const errorReporter = new ErrorReporter(requestId, SYNC_OPERATIONS.SYNC_ON_DEMAND);
  
  try {
    // Test 1: Basic logging
    console.log("\n📝 Test 1: Basic Logging");
    await logger.logSync(
      SYNC_EVENTS.SYNC_STARTED,
      { test: "data" },
      { result: "success" },
      "Test sync started",
      requestId,
      "test_entity",
      "entityId",
      { status: "ok" },
      SYNC_OPERATIONS.SYNC_ON_DEMAND
    );
    console.log("✅ Basic logging test passed");

    // Test 2: Error accumulation
    console.log("\n📊 Test 2: Error Accumulation");
    
    // Simulate some successful operations
    errorAccumulator.addSuccess("Account_Sync", "Account", "<EMAIL>");
    errorAccumulator.addSuccess("Opportunity_Sync", "Opportunity", "opp123");
    errorAccumulator.incrementProcessed();
    errorAccumulator.incrementProcessed();
    
    // Simulate some errors
    errorAccumulator.addError(
      "Task_Sync", 
      "Task", 
      "task456", 
      new Error("Network timeout"), 
      { timeout: 30000 }
    );
    errorAccumulator.addError(
      "Account_Sync", 
      "Account", 
      "invalid@email", 
      new Error("Validation failed: Required field missing"), 
      { field: "FirstName" }
    );
    
    // Simulate some warnings
    errorAccumulator.addWarning(
      "Opportunity_Sync",
      "Opportunity", 
      "opp789",
      "Record already exists, skipping",
      { existingId: "opp789" }
    );
    
    const stats = errorAccumulator.getStats();
    console.log("📈 Accumulator Stats:", stats);
    console.log("✅ Error accumulation test passed");

    // Test 3: Comprehensive error reporting
    console.log("\n📋 Test 3: Comprehensive Error Reporting");
    
    const comprehensiveReport = await errorReporter.generateComprehensiveReport(
      errorAccumulator,
      { test: "event" },
      "Test Sync Process"
    );
    
    console.log("📊 Comprehensive Report Summary:", {
      hasErrors: comprehensiveReport.hasErrors,
      hasWarnings: comprehensiveReport.hasWarnings,
      errorCount: comprehensiveReport.summary.errorCount,
      warningCount: comprehensiveReport.summary.warningCount,
      successRate: comprehensiveReport.summary.successRate
    });
    
    console.log("🔧 Recommendations:", comprehensiveReport.recommendations);
    console.log("✅ Comprehensive error reporting test passed");

    // Test 4: Notification content generation
    console.log("\n📧 Test 4: Notification Content Generation");
    
    const notificationContent = errorReporter.generateNotificationContent(
      errorAccumulator,
      "Test Sync Process"
    );
    
    console.log("📬 Notification Content:");
    console.log("Subject:", notificationContent.subject);
    console.log("Priority:", notificationContent.priority);
    console.log("Body Preview:", notificationContent.body.substring(0, 200) + "...");
    console.log("✅ Notification content generation test passed");

    // Test 5: Structured report
    console.log("\n📄 Test 5: Structured Report");
    
    const structuredReport = errorReporter.createStructuredReport(
      errorAccumulator,
      "Test Sync Process"
    );
    
    console.log("📋 Structured Report Metadata:", structuredReport.metadata);
    console.log("📊 Structured Report Metrics:", structuredReport.metrics);
    console.log("🚨 Error Count:", structuredReport.errors.length);
    console.log("⚠️ Warning Count:", structuredReport.warnings.length);
    console.log("✅ Structured report test passed");

    console.log("\n🎉 All logging integration tests passed successfully!");
    
    return {
      success: true,
      requestId,
      stats,
      comprehensiveReport,
      notificationContent,
      structuredReport
    };

  } catch (error) {
    console.error("❌ Logging integration test failed:", error);
    return {
      success: false,
      error: error.message,
      requestId
    };
  }
}

// Test the error accumulator's batch processing capability
async function testBatchProcessing() {
  console.log("\n🔄 Testing Batch Processing with Error Handling...");
  
  const errorAccumulator = new ErrorAccumulator();
  
  // Mock items to process
  const mockItems = [
    { id: "item1", email: "<EMAIL>", valid: true },
    { id: "item2", email: "<EMAIL>", valid: false },
    { id: "item3", email: "<EMAIL>", valid: true },
    { id: "item4", email: "<EMAIL>", valid: false },
    { id: "item5", email: "<EMAIL>", valid: true },
  ];
  
  // Mock processing function that sometimes fails
  const processItem = async (item) => {
    if (!item.valid) {
      throw new Error(`Validation failed for ${item.id}`);
    }
    return { processed: true, id: item.id };
  };
  
  // Function to extract entity info
  const getEntityInfo = (item) => ({
    entityType: "TestItem",
    entityId: item.id
  });
  
  // Process items with error handling
  const results = await errorAccumulator.processItemsWithErrorHandling(
    mockItems,
    processItem,
    "Test_Processing",
    getEntityInfo,
    { batchSize: 2, maxConcurrency: 3 }
  );
  
  console.log("📊 Batch Processing Results:");
  console.log("Total Items:", mockItems.length);
  console.log("Successful Results:", results.filter(r => r !== null).length);
  console.log("Failed Results:", results.filter(r => r === null).length);
  console.log("Final Stats:", errorAccumulator.getStats());
  
  console.log("✅ Batch processing test completed");
  
  return {
    totalItems: mockItems.length,
    successfulResults: results.filter(r => r !== null).length,
    failedResults: results.filter(r => r === null).length,
    stats: errorAccumulator.getStats()
  };
}

// Run tests if this file is executed directly
if (require.main === module) {
  (async () => {
    console.log("🚀 Starting Logging Integration Tests...\n");
    
    const testResult = await testLoggingIntegration();
    const batchResult = await testBatchProcessing();
    
    console.log("\n📋 Final Test Summary:");
    console.log("Main Test Success:", testResult.success);
    console.log("Batch Test Completed:", batchResult.totalItems > 0);
    
    if (testResult.success) {
      console.log("\n🎯 Integration is ready for use!");
      console.log("You can now use the logging services in your sync handlers.");
    } else {
      console.log("\n❌ Integration needs attention.");
      console.log("Error:", testResult.error);
    }
  })();
}

module.exports = {
  testLoggingIntegration,
  testBatchProcessing
};
